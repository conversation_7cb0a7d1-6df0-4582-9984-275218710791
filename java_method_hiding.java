// Java 方法隐藏（Method Hiding）详解

class Parent {
    // 静态方法
    public static void staticMethod() {
        System.out.println("Parent的静态方法");
    }
    
    // 实例方法
    public void instanceMethod() {
        System.out.println("Parent的实例方法");
    }
}

class Child extends Parent {
    // 这是方法隐藏（hiding），不是重写
    public static void staticMethod() {
        System.out.println("Child的静态方法");
    }
    
    // 这是方法重写（override）
    @Override
    public void instanceMethod() {
        System.out.println("Child的实例方法");
    }
}

public class MethodHidingDemo {
    public static void main(String[] args) {
        System.out.println("=== 方法隐藏 vs 方法重写 ===\n");
        
        // 1. 直接通过类名调用静态方法
        System.out.println("1. 通过类名调用静态方法：");
        Parent.staticMethod();  // 输出：Parent的静态方法
        Child.staticMethod();   // 输出：Child的静态方法
        
        // 2. 通过实例调用（不推荐）
        System.out.println("\n2. 通过实例调用：");
        Parent p1 = new Parent();
        Parent p2 = new Child();  // 父类引用指向子类对象
        Child c = new Child();
        
        p1.staticMethod();      // 输出：Parent的静态方法
        p2.staticMethod();      // 输出：Parent的静态方法（重点！）
        c.staticMethod();       // 输出：Child的静态方法
        
        // 3. 对比实例方法（重写）
        System.out.println("\n3. 实例方法调用（重写）：");
        p1.instanceMethod();    // 输出：Parent的实例方法
        p2.instanceMethod();    // 输出：Child的实例方法（多态！）
        c.instanceMethod();     // 输出：Child的实例方法
        
        // 4. 关键区别演示
        System.out.println("\n4. 隐藏 vs 重写的本质区别：");
        demonstrateDifference();
    }
    
    public static void demonstrateDifference() {
        Parent parent = new Child();
        
        // 静态方法：编译时绑定（早期绑定）
        // 调用哪个方法取决于引用类型，不是对象类型
        parent.staticMethod();      // Parent的静态方法
        ((Child)parent).staticMethod(); // Child的静态方法
        
        // 实例方法：运行时绑定（晚期绑定）
        // 调用哪个方法取决于对象的实际类型
        parent.instanceMethod();    // Child的实例方法
    }
}

/*
方法隐藏（Method Hiding）的特点：

1. 只发生在静态方法上
2. 子类定义了与父类相同签名的静态方法
3. 不是多态，是编译时决定的
4. 调用哪个方法取决于引用类型，而不是对象类型

方法隐藏 vs 方法重写：
- 隐藏：静态绑定，编译时确定
- 重写：动态绑定，运行时确定

记忆技巧：
- static方法属于类，不属于对象，所以不能被重写
- 实例方法属于对象，可以被重写，实现多态
*/