// Java 重写（Override）示例

// 父类
class Animal {
    // 父类方法
    public void makeSound() {
        System.out.println("动物发出声音");
    }
    
    // final方法不能被重写
    public final void breathe() {
        System.out.println("呼吸");
    }
    
    // static方法不是重写，而是隐藏
    public static void sleep() {
        System.out.println("动物睡觉");
    }
}

// 子类
class Dog extends Animal {
    // 重写父类的makeSound方法
    @Override
    public void makeSound() {
        System.out.println("狗在汪汪叫");
    }
    
    // 错误：不能重写final方法
    // public void breathe() { }
    
    // 这是方法隐藏，不是重写
    public static void sleep() {
        System.out.println("狗在睡觉");
    }
}

// 重写规则示例
class Parent {
    // 返回类型协变
    public Number calculate() {
        return 42;
    }
    
    // 访问权限
    protected void doSomething() {
        System.out.println("Parent doing something");
    }
    
    // 异常处理
    public void riskyMethod() throws Exception {
        System.out.println("Parent risky method");
    }
}

class Child extends Parent {
    // 协变返回类型（Integer是Number的子类）
    @Override
    public Integer calculate() {
        return 100;
    }
    
    // 可以扩大访问权限（protected -> public）
    @Override
    public void doSomething() {
        System.out.println("Child doing something");
    }
    
    // 可以不抛出异常或抛出更具体的异常
    @Override
    public void riskyMethod() {
        System.out.println("Child safe method");
    }
}

public class OverrideDemo {
    public static void main(String[] args) {
        // 多态示例
        Animal animal = new Dog();
        animal.makeSound(); // 输出：狗在汪汪叫（运行时多态）
        
        // static方法调用
        Animal.sleep(); // 输出：动物睡觉
        Dog.sleep();    // 输出：狗在睡觉
        
        // 通过父类引用调用static方法
        ((Animal) new Dog()).sleep(); // 输出：动物睡觉（编译时决定）
    }
}

/*
重写（Override）的关键点：
1. 发生在继承关系中
2. 方法签名必须相同（方法名、参数列表）
3. 返回类型相同或是其子类（协变返回类型）
4. 访问权限不能更严格
5. 不能抛出更宽泛的异常
6. @Override注解是可选的，但推荐使用
7. private、final、static方法不能被重写
8. 构造方法不能被重写
*/